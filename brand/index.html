<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vertoie - The AI Business Assistant That Learns Your Industry</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #1F2937;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(10px, 10px); }
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-content p {
            font-size: 1.25rem;
            color: rgba(255,255,255,0.95);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: white;
            color: #FF6B35;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            background: #FFF7F0;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 1rem 2rem;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.1);
            border-color: white;
        }

        .hero-demo {
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .demo-screen {
            background: #1F2937;
            border-radius: 12px;
            padding: 1.5rem;
            font-family: 'Courier New', monospace;
            color: #22C55E;
            font-size: 0.9rem;
            line-height: 1.4;
            position: relative;
            overflow: hidden;
        }

        .demo-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: scan 3s ease-in-out infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .typing-text {
            border-right: 2px solid #22C55E;
            animation: typing 4s steps(40) infinite, blink 1s step-end infinite;
            white-space: nowrap;
            overflow: hidden;
        }

        @keyframes typing {
            0%, 20% { width: 0; }
            30%, 70% { width: 100%; }
            80%, 100% { width: 0; }
        }

        @keyframes blink {
            0%, 50% { border-color: #22C55E; }
            51%, 100% { border-color: transparent; }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        nav.scrolled {
            background: rgba(255,255,255,0.98);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            height: 32px;
            width: auto;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: #1F2937;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #FF6B35;
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: #FAFAFA;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.2rem;
            color: #52525B;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #52525B;
            line-height: 1.6;
        }

        /* Demo Section */
        .demo-section {
            padding: 6rem 0;
            background: white;
        }

        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .demo-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 1.5rem;
        }

        .demo-steps {
            margin: 2rem 0;
        }

        .demo-step {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .step-number {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .demo-visual {
            background: #F4F4F5;
            border-radius: 16px;
            padding: 2rem;
            position: relative;
        }

        .phone-mockup {
            background: #1F2937;
            border-radius: 24px;
            padding: 1rem;
            max-width: 300px;
            margin: 0 auto;
            position: relative;
        }

        .phone-screen {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            min-height: 400px;
        }

        .voice-indicator {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            display: inline-block;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Pricing Section */
        .pricing {
            padding: 6rem 0;
            background: linear-gradient(135deg, #FFF7F0 0%, #FFE5D9 100%);
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 2.5rem;
            text-align: center;
            position: relative;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .pricing-card.featured {
            border: 2px solid #FF6B35;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 16px 50px rgba(0,0,0,0.15);
        }

        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-5px);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 1rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 700;
            color: #FF6B35;
            margin-bottom: 0.5rem;
        }

        .plan-price span {
            font-size: 1rem;
            color: #52525B;
            font-weight: 400;
        }

        .plan-description {
            color: #52525B;
            margin-bottom: 2rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .plan-features li {
            padding: 0.5rem 0;
            color: #52525B;
            position: relative;
            padding-left: 1.5rem;
        }

        .plan-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #22C55E;
            font-weight: 600;
        }

        /* CTA Section */
        .cta-section {
            padding: 6rem 0;
            background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
            color: white;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* Footer */
        footer {
            background: #111827;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #FF6B35;
        }

        .footer-section a {
            color: #D4D4D8;
            text-decoration: none;
            display: block;
            padding: 0.25rem 0;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #FF6B35;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 1rem;
            text-align: center;
            color: #A1A1AA;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .demo-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .nav-links {
                display: none;
            }

            .cta-buttons {
                justify-content: center;
            }
        }

        /* Scroll animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav id="navbar">
        <div class="nav-container">
            <svg class="logo" viewBox="0 23.1 161.11 37.540001" width="161.11" height="37.540001">
                <defs>
                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#FF6B35"/>
                        <stop offset="100%" style="stop-color:#F7931E"/>
                    </linearGradient>
                </defs>
                <path d="m 0,33.86 h 6.62 l 6.22,19.73 6.15,-19.73 h 6.28 L 16.38,60.08 H 9.33 Z M 29.13,51.88 V 41.65 q 0,-4 3.04,-6.18 3.04,-2.17 8.14,-2.17 6.05,0 8.6,2.32 2.55,2.32 2.55,6.27 v 6.35 H 35.77 v 4.27 q 0,2.32 1.19,2.92 1.2,0.6 3.57,0.6 2.83,0 3.79,-0.79 0.97,-0.8 0.97,-3.21 h 6.15 q 0,4.85 -2.73,6.73 -2.74,1.88 -8.3,1.88 -3.2,0 -5.77,-0.79 -2.56,-0.79 -4.04,-2.7 -1.47,-1.9 -1.47,-5.27 m 16.06,-7.69 v -3.08 q 0,-1.46 -0.62,-2.13 -0.62,-0.67 -1.69,-0.86 -1.06,-0.18 -2.4,-0.18 -2,0 -3.36,0.6 -1.35,0.6 -1.35,2.7 v 2.95 z M 58.06,33.86 h 6.64 v 3.13 q 0.58,-1.03 2.06,-1.82 1.48,-0.79 3.38,-1.25 1.91,-0.45 3.81,-0.45 l -0.15,5.64 q -1.27,0.07 -2.76,0.32 -1.48,0.24 -2.86,0.66 -1.38,0.41 -2.34,1.02 -0.95,0.61 -1.14,1.39 V 60.08 H 58.06 Z M 79.47,51.61 V 38.09 H 76 v -4.23 h 3.66 l 0.61,-8.42 h 5.84 v 8.42 h 6.22 v 4.23 h -6.22 v 13.03 q 0,2.44 1.06,3.38 1.06,0.94 4.07,0.94 h 1.26 l -0.24,4.94 h -1.81 q -3.95,0 -6.37,-0.79 -2.41,-0.78 -3.51,-2.69 -1.1,-1.92 -1.1,-5.29 z m 29.2,9.03 q -6.2,0 -8.79,-2.29 -2.59,-2.3 -2.59,-6.2 V 41.82 q 0,-4 2.99,-6.26 2.99,-2.26 8.39,-2.26 5.37,0 8.37,2.26 3,2.26 3,6.26 v 10.33 q 0,2.59 -1.07,4.5 -1.07,1.92 -3.55,2.96 -2.48,1.03 -6.75,1.03 m 0,-4.61 q 2.12,0 3.43,-0.79 1.3,-0.8 1.3,-2.8 V 41.53 q 0,-2 -1.24,-2.8 -1.25,-0.79 -3.49,-0.79 -2.15,0 -3.45,0.79 -1.29,0.8 -1.29,2.8 v 10.91 q 0,2 1.22,2.8 1.22,0.79 3.52,0.79 z M 126.1,33.86 h 6.61 V 60.08 H 126.1 V 33.86 M 126.05,23.1 h 6.69 v 6.47 h -6.69 z m 12.72,28.78 V 41.65 q 0,-4 3.04,-6.18 3.04,-2.17 8.14,-2.17 6.06,0 8.61,2.32 2.55,2.32 2.55,6.27 v 6.35 h -15.7 v 4.27 q 0,2.32 1.2,2.92 1.19,0.6 3.56,0.6 2.83,0 3.8,-0.79 0.96,-0.8 0.96,-3.21 h 6.15 q 0,4.85 -2.73,6.73 -2.73,1.88 -8.3,1.88 -3.2,0 -5.76,-0.79 -2.57,-0.79 -4.04,-2.7 -1.48,-1.9 -1.48,-5.27 m 16.06,-7.69 v -3.08 q 0,-1.46 -0.62,-2.13 -0.62,-0.67 -1.68,-0.86 -1.07,-0.18 -2.41,-0.18 -2,0 -3.35,0.6 -1.36,0.6 -1.36,2.7 v 2.95 z" fill="url(#logoGradient)"/>
            </svg>
            
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>The AI Business Assistant That Learns Your Industry</h1>
                <p>Transform any business into a streamlined, voice-controlled operation. Tell us your industry and we'll instantly generate custom modules, workflows, and processes designed specifically for your business.</p>
                <div class="cta-buttons">
                    <a href="#demo" class="btn-primary">Start Free Trial</a>
                    <a href="#demo" class="btn-secondary">Watch Demo</a>
                </div>
            </div>
            
            <div class="hero-demo">
                <div class="demo-screen">
                    <div class="typing-text">> "I run a landscaping business"</div>
                    <br>
                    <div style="color: #FBBF24;">🔄 Generating custom modules...</div>
                    <div style="color: #22C55E;">✅ Seasonal planning tools</div>
                    <div style="color: #22C55E;">✅ Plant health tracking</div>
                    <div style="color: #22C55E;">✅ Weather integration</div>
                    <div style="color: #22C55E;">✅ Custom invoicing templates</div>
                    <br>
                    <div style="color: #3B82F6;">💡 Your personalized business OS is ready!</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header fade-in">
                <h2>Revolutionary AI-Powered Business Management</h2>
                <p>Instead of forcing your business into generic tools, our AI creates a completely personalized business operating system designed specifically for your industry.</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon">🤖</div>
                    <h3>Instant AI Module Generation</h3>
                    <p>Tell us "I run a pool service" and instantly get chemical tracking, route optimization, and equipment logs. Any industry, any business type - custom modules in seconds.</p>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon">📱</div>
                    <h3>Cross-Platform App Deployment</h3>
                    <p>Your custom business application deploys everywhere - native desktop apps, mobile apps, and web applications. One codebase, unlimited deployment options powered by modern Tauri technology.</p>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon">⚡</div>
                    <h3>Smart Workflow Automation</h3>
                    <p>Complete a maintenance task? Invoice automatically generated. Client payment overdue? Reminder sent. Your business processes run themselves while you focus on growth.</p>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon">💬</div>
                    <h3>Conversational Business Modeling</h3>
                    <p>Simply describe your business in natural language and watch as AI generates custom modules, data models, and workflows tailored specifically to your industry and unique requirements.</p>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon">📊</div>
                    <h3>Predictive Business Intelligence</h3>
                    <p>Cash flow forecasting, client behavior predictions, and market rate recommendations powered by industry-specific AI that learns from your business patterns.</p>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon">🔗</div>
                    <h3>Seamless Integrations</h3>
                    <p>Connect with QuickBooks, Stripe, Google Calendar, and 100+ business tools. Your existing workflow enhanced, not replaced, with intelligent automation.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section class="demo-section" id="demo">
        <div class="container">
            <div class="demo-container">
                <div class="demo-content fade-in">
                    <h2>See Vertoie In Action</h2>
                    <p>Watch how businesses transform from generic tools to industry-specific powerhouses in minutes, not months.</p>
                    
                    <div class="demo-steps">
                        <div class="demo-step">
                            <div class="step-number">1</div>
                            <div>
                                <strong>Describe Your Business</strong><br>
                                "I run a property management company with 50 units"
                            </div>
                        </div>
                        
                        <div class="demo-step">
                            <div class="step-number">2</div>
                            <div>
                                <strong>AI Generates Custom Modules</strong><br>
                                Tenant portals, maintenance requests, lease tracking - instantly built
                            </div>
                        </div>
                        
                        <div class="demo-step">
                            <div class="step-number">3</div>
                            <div>
                                <strong>Voice Commands Take Control</strong><br>
                                "Bill unit 4B for emergency plumbing repair" - Done.
                            </div>
                        </div>
                        
                        <div class="demo-step">
                            <div class="step-number">4</div>
                            <div>
                                <strong>Business Runs Itself</strong><br>
                                Automated workflows, predictive insights, effortless operations
                            </div>
                        </div>
                    </div>
                    
                    <a href="#pricing" class="btn-primary">Start Your Transformation</a>
                </div>
                
                <div class="demo-visual fade-in">
                    <div class="phone-mockup">
                        <div class="phone-screen">
                            <div class="voice-indicator">🎤 Listening...</div>
                            <div style="margin-bottom: 1rem;">
                                <strong>"Bill Martinez Pools for 3 hours of chemical balancing"</strong>
                            </div>
                            <div style="background: #F4F4F5; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                                <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Client: Martinez Pools</div>
                                <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Service: Chemical Balancing</div>
                                <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Hours: 3.0</div>
                                <div style="color: #22C55E;">✅ Rate: $85/hour (from client history)</div>
                            </div>
                            <div style="background: linear-gradient(135deg, #FF6B35, #F7931E); color: white; padding: 1rem; border-radius: 8px; text-align: center;">
                                <strong>Invoice #2847 Created</strong><br>
                                Total: $255.00<br>
                                <small><NAME_EMAIL></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-header fade-in">
                <h2>Choose Your Growth Plan</h2>
                <p>Start with our powerful free tier and scale as your business grows. Every plan includes unlimited AI module generation.</p>
            </div>
            
            <div class="pricing-grid">
                <div class="pricing-card fade-in">
                    <div class="plan-name">Starter</div>
                    <div class="plan-price">Free<span></span></div>
                    <div class="plan-description">Perfect for testing Vertoie with your business</div>
                    <ul class="plan-features">
                        <li>Up to 3 AI-generated modules</li>
                        <li>Basic voice commands</li>
                        <li>50 invoices per month</li>
                        <li>Email support</li>
                        <li>Core integrations</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Start Free</a>
                </div>
                
                <div class="pricing-card featured fade-in">
                    <div class="plan-name">Professional</div>
                    <div class="plan-price">$49<span>/month</span></div>
                    <div class="plan-description">The complete business OS for growing companies</div>
                    <ul class="plan-features">
                        <li>Unlimited AI modules</li>
                        <li>Advanced voice commands</li>
                        <li>Unlimited invoices</li>
                        <li>Predictive analytics</li>
                        <li>Priority support</li>
                        <li>All integrations</li>
                        <li>Custom workflows</li>
                        <li>Team collaboration</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Start 14-Day Trial</a>
                </div>
                
                <div class="pricing-card fade-in">
                    <div class="plan-name">Enterprise</div>
                    <div class="plan-price">$199<span>/month</span></div>
                    <div class="plan-description">Full-scale solution for multiple locations</div>
                    <ul class="plan-features">
                        <li>Everything in Professional</li>
                        <li>Multi-location management</li>
                        <li>Custom AI model training</li>
                        <li>White-label options</li>
                        <li>Dedicated account manager</li>
                        <li>Custom integrations</li>
                        <li>SLA guarantees</li>
                        <li>Advanced security</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2>Ready to Transform Your Business?</h2>
            <p>Join thousands of businesses already using AI to streamline their operations. Get your custom business OS in minutes.</p>
            <div class="cta-buttons">
                <a href="#contact" class="btn-primary">Start Free Trial</a>
                <a href="#demo" class="btn-secondary">Schedule Demo</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Product</h3>
                    <a href="#features">Features</a>
                    <a href="#pricing">Pricing</a>
                    <a href="#demo">Demo</a>
                    <a href="#">Integrations</a>
                    <a href="#">API</a>
                </div>
                
                <div class="footer-section">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                    <a href="#">Contact</a>
                </div>
                
                <div class="footer-section">
                    <h3>Resources</h3>
                    <a href="#">Documentation</a>
                    <a href="#">Help Center</a>
                    <a href="#">Community</a>
                    <a href="#">Blog</a>
                </div>
                
                <div class="footer-section">
                    <h3>Legal</h3>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Security</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 Vertoie. All rights reserved. Built for businesses that refuse to settle for generic tools.</p>
            </div>
        </div>
    </footer>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Demo typing effect
        let demoInterval;
        function startDemoAnimation() {
            const demoTexts = [
                '> "I run a landscaping business"',
                '> "I manage 25 rental properties"',
                '> "I own a pool cleaning service"',
                '> "I run an HVAC company"'
            ];
            let currentIndex = 0;
            
            demoInterval = setInterval(() => {
                const typingElement = document.querySelector('.typing-text');
                if (typingElement) {
                    currentIndex = (currentIndex + 1) % demoTexts.length;
                    typingElement.textContent = demoTexts[currentIndex];
                }
            }, 4000);
        }

        // Start demo animation when page loads
        window.addEventListener('load', startDemoAnimation);
    </script>
</body>
</html>